// Error messages and user feedback constants

export const ERROR_MESSAGES = {
  // File upload errors
  FILE_TOO_LARGE: 'File too large. Maximum size is 5MB.',
  UNSUPPORTED_FORMAT: 'Unsupported file type. Please upload a JPG or PNG image.',
  NO_FILE_SELECTED: 'Please select an image file.',
  UPLOAD_FAILED: 'Failed to upload image. Please try again.',

  // Image processing errors
  NO_OBJECT_DETECTED: 'No object detected. Please upload another image.',
  PROCESSING_FAILED: 'An error occurred during processing. Please try again later.',
  INVALID_IMAGE_DATA: 'Invalid image data. Please upload a valid image.',

  // General errors
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
  SERVER_ERROR: 'Server error. Please try again later.',

  // Validation errors
  INVALID_TEXT_CONTENT: 'Text content cannot be empty.',
  INVALID_COLOR: 'Please select a valid color.',
  INVALID_FONT: 'Please select a valid font.',
  INVALID_POSITION: 'Invalid text position.',
} as const;

export const SUCCESS_MESSAGES = {
  IMAGE_UPLOADED: 'Image uploaded successfully!',
  IMAGE_PROCESSED: 'Image processed successfully!',
  DOWNLOAD_READY: 'Your image is ready for download!',
  TEXT_UPDATED: 'Text updated successfully!',
} as const;

export const INFO_MESSAGES = {
  PROCESSING_IMAGE: 'Processing image...',
  DETECTING_OBJECT: 'Detecting object...',
  GENERATING_IMAGE: 'Generating final image...',
  UPLOADING: 'Uploading image...',
} as const;

export type ErrorMessageKey = keyof typeof ERROR_MESSAGES;
export type SuccessMessageKey = keyof typeof SUCCESS_MESSAGES;
export type InfoMessageKey = keyof typeof INFO_MESSAGES;
