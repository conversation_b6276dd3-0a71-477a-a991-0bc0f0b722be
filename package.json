{"name": "text-behind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"canvas": "^3.1.2", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.33.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}