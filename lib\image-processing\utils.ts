// Image processing utility functions

import type { ImageDimensions, ObjectDetectionResult } from '@/types/image';
import type { TextPosition } from '@/types/text';

/**
 * Calculates optimal text position behind detected object
 */
export function calculateTextPosition(
  objectBounds: ObjectDetectionResult,
  imageDimensions: ImageDimensions,
  textDimensions: { width: number; height: number }
): TextPosition {
  if (!objectBounds.detected || !objectBounds.boundingBox) {
    // Default center position if no object detected
    return {
      x: (imageDimensions.width - textDimensions.width) / 2,
      y: (imageDimensions.height - textDimensions.height) / 2,
    };
  }

  const { boundingBox } = objectBounds;
  
  // Position text behind the object (slightly offset)
  const offsetX = boundingBox.width * 0.1; // 10% offset
  const offsetY = boundingBox.height * 0.1; // 10% offset
  
  const textX = Math.max(
    0,
    Math.min(
      boundingBox.x - textDimensions.width - offsetX,
      imageDimensions.width - textDimensions.width
    )
  );
  
  const textY = Math.max(
    0,
    Math.min(
      boundingBox.y + (boundingBox.height - textDimensions.height) / 2,
      imageDimensions.height - textDimensions.height
    )
  );

  return { x: textX, y: textY };
}

/**
 * Validates image dimensions
 */
export function validateImageDimensions(dimensions: ImageDimensions): boolean {
  return (
    dimensions.width > 0 &&
    dimensions.height > 0 &&
    dimensions.width <= 4096 &&
    dimensions.height <= 4096
  );
}

/**
 * Calculates text dimensions based on font and content
 */
export function calculateTextDimensions(
  text: string,
  fontSize: number,
  fontFamily: string
): { width: number; height: number } {
  // Create a temporary canvas to measure text
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  
  if (!context) {
    // Fallback estimation
    return {
      width: text.length * fontSize * 0.6,
      height: fontSize * 1.2,
    };
  }

  context.font = `${fontSize}px ${fontFamily}`;
  const metrics = context.measureText(text);
  
  return {
    width: metrics.width,
    height: fontSize * 1.2, // Approximate height
  };
}

/**
 * Converts image to different format
 */
export function convertImageFormat(
  imageData: string,
  targetFormat: 'png' | 'jpg',
  quality: number = 0.9
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      
      // Fill with white background for JPG
      if (targetFormat === 'jpg') {
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }
      
      ctx.drawImage(img, 0, 0);
      
      const mimeType = targetFormat === 'png' ? 'image/png' : 'image/jpeg';
      const result = canvas.toDataURL(mimeType, quality);
      resolve(result);
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = imageData;
  });
}
