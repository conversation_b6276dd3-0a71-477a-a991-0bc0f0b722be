// Image-related type definitions for Text-Behind app

export interface ImageFile {
  file: File;
  preview: string;
  name: string;
  size: number;
  type: string;
}

export interface ImageDimensions {
  width: number;
  height: number;
}

export interface ProcessedImage {
  url: string;
  dimensions: ImageDimensions;
  format: 'png' | 'jpg';
}

export interface ImageUploadResponse {
  success: boolean;
  message: string;
  imageUrl?: string;
  error?: string;
}

export interface ImageProcessingRequest {
  imageData: string; // base64 encoded image
  textContent: string;
  textColor: string;
  fontFamily: string;
  fontSize: number;
  textPosition: TextPosition;
}

export interface ImageProcessingResponse {
  success: boolean;
  processedImageUrl?: string;
  error?: string;
  message: string;
}

export interface TextPosition {
  x: number;
  y: number;
}

export interface ObjectDetectionResult {
  detected: boolean;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  confidence?: number;
}

export type SupportedImageFormat = 'image/jpeg' | 'image/png';
export type OutputFormat = 'png' | 'jpg';
