'use client';

import { useState, useCallback } from 'react';
import { TEXT_DEFAULTS } from '@/constants/app-config';
import type { TextCustomization, TextPosition } from '@/types/text';

export function useTextCustomization() {
  const [textCustomization, setTextCustomization] = useState<TextCustomization>({
    content: TEXT_DEFAULTS.content,
    color: TEXT_DEFAULTS.color,
    fontFamily: TEXT_DEFAULTS.fontFamily,
    fontSize: TEXT_DEFAULTS.fontSize,
    position: { ...TEXT_DEFAULTS.position },
  });

  const updatePosition = useCallback((position: TextPosition) => {
    setTextCustomization(prev => ({
      ...prev,
      position,
    }));
  }, []);

  const updateContent = useCallback((content: string) => {
    setTextCustomization(prev => ({
      ...prev,
      content,
    }));
  }, []);

  const updateColor = useCallback((color: string) => {
    setTextCustomization(prev => ({
      ...prev,
      color,
    }));
  }, []);

  const updateFontFamily = useCallback((fontFamily: string) => {
    setTextCustomization(prev => ({
      ...prev,
      fontFamily,
    }));
  }, []);

  const updateFontSize = useCallback((fontSize: number) => {
    setTextCustomization(prev => ({
      ...prev,
      fontSize,
    }));
  }, []);

  const resetToDefaults = useCallback(() => {
    setTextCustomization({
      content: TEXT_DEFAULTS.content,
      color: TEXT_DEFAULTS.color,
      fontFamily: TEXT_DEFAULTS.fontFamily,
      fontSize: TEXT_DEFAULTS.fontSize,
      position: { ...TEXT_DEFAULTS.position },
    });
  }, []);

  return {
    textCustomization,
    updatePosition,
    updateContent,
    updateColor,
    updateFontFamily,
    updateFontSize,
    resetToDefaults,
  };
}
