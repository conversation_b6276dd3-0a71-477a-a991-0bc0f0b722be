// API route for image processing

import { NextRequest, NextResponse } from 'next/server';
import { addTextBehindObject, getImageMetadata, convertImageFormat } from '@/lib/image-processing/server-text-overlay';
import { detectObject } from '@/lib/image-processing/object-detection';
import { TEXT_DEFAULTS } from '@/constants/app-config';
import type { ImageProcessingRequest, ImageProcessingResponse } from '@/types/image';
import type { TextCustomization } from '@/types/text';

export async function POST(request: NextRequest): Promise<NextResponse<ImageProcessingResponse>> {
  try {
    const body: ImageProcessingRequest = await request.json();

    // Validate request body
    if (!body.imageData) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields',
        message: 'Image data is required',
      }, { status: 400 });
    }

    // Extract base64 image data
    const base64Data = body.imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Get image metadata
    const metadata = await getImageMetadata(imageBuffer);

    // Prepare text customization (use defaults if not provided)
    const textCustomization: TextCustomization = {
      content: body.textContent || TEXT_DEFAULTS.content,
      color: body.textColor || TEXT_DEFAULTS.color,
      fontFamily: body.fontFamily || TEXT_DEFAULTS.fontFamily,
      fontSize: body.fontSize || TEXT_DEFAULTS.fontSize,
      position: body.textPosition || TEXT_DEFAULTS.position,
    };

    // Perform object detection (mock for now)
    const detectionResult = await detectObject(body.imageData, {
      width: metadata.width,
      height: metadata.height,
    });

    // If no object detected, return error
    if (!detectionResult.detected) {
      return NextResponse.json({
        success: false,
        error: 'No object detected',
        message: 'No object detected. Please try another image.',
      }, { status: 400 });
    }

    // Add text behind detected object
    const processedBuffer = await addTextBehindObject({
      imageBuffer,
      text: textCustomization,
      imageDimensions: {
        width: metadata.width,
        height: metadata.height,
      },
      objectDetection: detectionResult,
    });

    // Convert processed image to base64
    const processedImageBase64 = `data:image/png;base64,${processedBuffer.toString('base64')}`;

    return NextResponse.json({
      success: true,
      processedImageUrl: processedImageBase64,
      message: 'Image processed successfully',
    });

  } catch (error) {
    console.error('Image processing error:', error);

    return NextResponse.json({
      success: false,
      error: 'Processing failed',
      message: 'An error occurred while processing the image',
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
