// API route for image processing

import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import type { ImageProcessingRequest, ImageProcessingResponse } from '@/types/image';

export async function POST(request: NextRequest): Promise<NextResponse<ImageProcessingResponse>> {
  try {
    const body: ImageProcessingRequest = await request.json();
    
    // Validate request body
    if (!body.imageData || !body.textContent) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields',
        message: 'Image data and text content are required',
      }, { status: 400 });
    }

    // Extract base64 image data
    const base64Data = body.imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Get image metadata
    const metadata = await sharp(imageBuffer).metadata();
    
    if (!metadata.width || !metadata.height) {
      return NextResponse.json({
        success: false,
        error: 'Invalid image',
        message: 'Could not determine image dimensions',
      }, { status: 400 });
    }

    // For now, we'll just return the original image
    // TODO: Implement actual object detection and text overlay
    const processedBuffer = await sharp(imageBuffer)
      .png()
      .toBuffer();

    // Convert processed image to base64
    const processedImageBase64 = `data:image/png;base64,${processedBuffer.toString('base64')}`;

    return NextResponse.json({
      success: true,
      processedImageUrl: processedImageBase64,
      message: 'Image processed successfully',
    });

  } catch (error) {
    console.error('Image processing error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Processing failed',
      message: 'An error occurred while processing the image',
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
