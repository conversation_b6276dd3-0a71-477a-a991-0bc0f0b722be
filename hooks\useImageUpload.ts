'use client';

import { useState, useCallback } from 'react';
import { revokeFilePreview } from '@/utils/helpers';
import type { ImageFile } from '@/types/image';

export function useImageUpload() {
  const [selectedImage, setSelectedImage] = useState<ImageFile | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const selectImage = useCallback((imageFile: ImageFile) => {
    // Clean up previous preview URL
    if (selectedImage?.preview) {
      revokeFilePreview(selectedImage.preview);
    }
    
    setSelectedImage(imageFile);
  }, [selectedImage]);

  const clearImage = useCallback(() => {
    if (selectedImage?.preview) {
      revokeFilePreview(selectedImage.preview);
    }
    setSelectedImage(null);
    setIsUploading(false);
  }, [selectedImage]);

  const setUploading = useCallback((uploading: boolean) => {
    setIsUploading(uploading);
  }, []);

  return {
    selectedImage,
    isUploading,
    selectImage,
    clearImage,
    setUploading,
  };
}
