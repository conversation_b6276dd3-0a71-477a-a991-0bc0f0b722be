// Text customization type definitions for Text-Behind app

export interface TextCustomization {
  content: string;
  color: string;
  fontFamily: string;
  fontSize: number;
  position: TextPosition;
}

export interface TextPosition {
  x: number;
  y: number;
}

export interface FontOption {
  name: string;
  value: string;
  preview: string;
}

export interface ColorOption {
  name: string;
  value: string;
  hex: string;
}

export interface TextDefaults {
  content: string;
  color: string;
  fontFamily: string;
  fontSize: number;
  position: TextPosition;
}

export interface TextEditingState {
  isEditing: boolean;
  currentText: TextCustomization;
  hasChanges: boolean;
}

export type TextAlignment = 'left' | 'center' | 'right';
export type FontWeight = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
