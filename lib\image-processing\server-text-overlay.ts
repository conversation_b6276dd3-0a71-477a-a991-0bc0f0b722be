// Server-side text overlay using <PERSON> with behind-object placement

import sharp from 'sharp';
import type { TextCustomization } from '@/types/text';
import type { ImageDimensions, ObjectDetectionResult } from '@/types/image';

interface ServerTextOverlayOptions {
  imageBuffer: Buffer;
  text: TextCustomization;
  imageDimensions: ImageDimensions;
  objectDetection: ObjectDetectionResult;
}

/**
 * Creates an SVG text element for Sharp to composite
 */
function createTextSVG(
  text: TextCustomization,
  imageDimensions: ImageDimensions,
  objectDetection?: ObjectDetectionResult
): string {
  const { content, color, fontFamily, fontSize } = text;

  // Calculate text position using percentage-based positioning
  // This allows users to control exactly where the text appears
  const x = (text.position.x / 100) * imageDimensions.width;
  const y = (text.position.y / 100) * imageDimensions.height;

  // Create SVG with text
  const svg = `
    <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <text
        x="${x}"
        y="${y}"
        font-family="${fontFamily}"
        font-size="${fontSize}"
        fill="${color}"
        text-anchor="start"
        dominant-baseline="middle"
        style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.7)); font-weight: bold;"
      >${content}</text>
    </svg>
  `;

  return svg;
}

/**
 * Creates an object mask based on detection results
 */
async function createObjectMask(
  imageDimensions: ImageDimensions,
  objectDetection: ObjectDetectionResult
): Promise<Buffer> {
  if (!objectDetection.detected || !objectDetection.boundingBox) {
    // Return empty mask if no object detected
    const emptyMask = `
      <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="black"/>
      </svg>
    `;
    return Buffer.from(emptyMask);
  }

  const { boundingBox } = objectDetection;

  // Create a simple rectangular mask for the detected object
  // In a real implementation, this would be a precise segmentation mask
  const maskSVG = `
    <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="black"/>
      <ellipse
        cx="${boundingBox.x + boundingBox.width / 2}"
        cy="${boundingBox.y + boundingBox.height / 2}"
        rx="${boundingBox.width / 2}"
        ry="${boundingBox.height / 2}"
        fill="white"/>
    </svg>
  `;

  return Buffer.from(maskSVG);
}

/**
 * Adds text behind detected object using layer compositing
 */
export async function addTextBehindObject(options: ServerTextOverlayOptions): Promise<Buffer> {
  const { imageBuffer, text, imageDimensions, objectDetection } = options;

  try {
    // Step 1: Create the text layer
    const textSVG = createTextSVG(text, imageDimensions, objectDetection);
    const textBuffer = Buffer.from(textSVG);

    // Step 2: Create object mask
    const objectMask = await createObjectMask(imageDimensions, objectDetection);

    // Step 3: Create background with text
    const backgroundWithText = await sharp({
      create: {
        width: imageDimensions.width,
        height: imageDimensions.height,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      }
    })
    .composite([
      {
        input: textBuffer,
        top: 0,
        left: 0,
      }
    ])
    .png()
    .toBuffer();

    // Step 4: Apply mask to original image to extract object
    const maskedObject = await sharp(imageBuffer)
      .composite([
        {
          input: objectMask,
          blend: 'dest-in'
        }
      ])
      .png()
      .toBuffer();

    // Step 5: Create inverted mask for background
    const invertedMask = await sharp(objectMask)
      .negate()
      .toBuffer();

    // Step 6: Apply inverted mask to original image to get background
    const maskedBackground = await sharp(imageBuffer)
      .composite([
        {
          input: invertedMask,
          blend: 'dest-in'
        }
      ])
      .png()
      .toBuffer();

    // Step 7: Composite everything together: background + text + object
    const result = await sharp(maskedBackground)
      .composite([
        {
          input: backgroundWithText,
          blend: 'over'
        },
        {
          input: maskedObject,
          blend: 'over'
        }
      ])
      .png()
      .toBuffer();

    return result;
  } catch (error) {
    console.error('Error adding text behind object:', error);
    throw new Error('Failed to add text behind object');
  }
}

/**
 * Legacy function - adds text overlay on top (fallback)
 */
export async function addTextOverlayToImage(options: ServerTextOverlayOptions): Promise<Buffer> {
  const { imageBuffer, text, imageDimensions } = options;

  try {
    // Create SVG text overlay
    const textSVG = createTextSVG(text, imageDimensions);
    const textBuffer = Buffer.from(textSVG);

    // Composite text onto image
    const result = await sharp(imageBuffer)
      .composite([
        {
          input: textBuffer,
          top: 0,
          left: 0,
        }
      ])
      .png()
      .toBuffer();

    return result;
  } catch (error) {
    console.error('Error adding text overlay:', error);
    throw new Error('Failed to add text overlay to image');
  }
}

/**
 * Converts image to specified format with quality settings
 */
export async function convertImageFormat(
  imageBuffer: Buffer,
  format: 'png' | 'jpg',
  quality: number = 90
): Promise<Buffer> {
  try {
    const sharpInstance = sharp(imageBuffer);
    
    if (format === 'jpg') {
      return await sharpInstance
        .jpeg({ quality })
        .toBuffer();
    } else {
      return await sharpInstance
        .png({ quality })
        .toBuffer();
    }
  } catch (error) {
    console.error('Error converting image format:', error);
    throw new Error('Failed to convert image format');
  }
}

/**
 * Gets image metadata and dimensions
 */
export async function getImageMetadata(imageBuffer: Buffer): Promise<{
  width: number;
  height: number;
  format: string;
}> {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    
    if (!metadata.width || !metadata.height) {
      throw new Error('Could not determine image dimensions');
    }
    
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format || 'unknown',
    };
  } catch (error) {
    console.error('Error getting image metadata:', error);
    throw new Error('Failed to get image metadata');
  }
}
