// Server-side text overlay using <PERSON> with behind-object placement

import sharp from 'sharp';
import type { TextCustomization } from '@/types/text';
import type { ImageDimensions, ObjectDetectionResult } from '@/types/image';

interface ServerTextOverlayOptions {
  imageBuffer: Buffer;
  text: TextCustomization;
  imageDimensions: ImageDimensions;
  objectDetection: ObjectDetectionResult;
}

/**
 * Creates an SVG text element for Sharp to composite
 */
function createTextSVG(
  text: TextCustomization,
  imageDimensions: ImageDimensions,
  objectDetection?: ObjectDetectionResult
): string {
  const { content, color, fontFamily, fontSize } = text;

  // Calculate text position using percentage-based positioning
  // This allows users to control exactly where the text appears
  let x = (text.position.x / 100) * imageDimensions.width;
  let y = (text.position.y / 100) * imageDimensions.height;

  // Ensure text is within bounds and visible
  x = Math.max(50, Math.min(x, imageDimensions.width - 200));
  y = Math.max(100, Math.min(y, imageDimensions.height - 50));

  // Debug logging
  console.log('Text positioning:', {
    content: text.content,
    position: text.position,
    calculatedX: x,
    calculatedY: y,
    imageDimensions,
    fontSize: text.fontSize,
    color: text.color
  });

  // Create SVG with text - make it very visible for debugging
  const svg = `
    <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <rect x="0" y="0" width="100%" height="100%" fill="none"/>
      <text
        x="${x}"
        y="${y}"
        font-family="Arial, sans-serif"
        font-size="120"
        fill="red"
        text-anchor="start"
        dominant-baseline="middle"
        stroke="white"
        stroke-width="4"
        style="font-weight: bold;"
      >POV</text>
    </svg>
  `;

  return svg;
}

/**
 * Creates an object mask based on detection results
 */
async function createObjectMask(
  imageDimensions: ImageDimensions,
  objectDetection: ObjectDetectionResult
): Promise<Buffer> {
  if (!objectDetection.detected || !objectDetection.boundingBox) {
    // Return empty mask if no object detected
    const emptyMask = `
      <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="black"/>
      </svg>
    `;
    return Buffer.from(emptyMask);
  }

  const { boundingBox } = objectDetection;

  // Create a simple rectangular mask for the detected object
  // In a real implementation, this would be a precise segmentation mask
  const maskSVG = `
    <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="black"/>
      <ellipse
        cx="${boundingBox.x + boundingBox.width / 2}"
        cy="${boundingBox.y + boundingBox.height / 2}"
        rx="${boundingBox.width / 2}"
        ry="${boundingBox.height / 2}"
        fill="white"/>
    </svg>
  `;

  return Buffer.from(maskSVG);
}

/**
 * Adds text behind detected object using layer compositing
 * Simplified version for debugging
 */
export async function addTextBehindObject(options: ServerTextOverlayOptions): Promise<Buffer> {
  const { imageBuffer, text, imageDimensions, objectDetection } = options;

  try {
    console.log('Starting text behind object processing...');

    // For now, let's just add text overlay to make sure text is visible
    // We'll add the behind-object effect once we confirm text is working
    const textSVG = createTextSVG(text, imageDimensions, objectDetection);
    const textBuffer = Buffer.from(textSVG);

    console.log('Created text SVG:', textSVG.substring(0, 200) + '...');

    // Simple overlay for debugging
    const result = await sharp(imageBuffer)
      .composite([
        {
          input: textBuffer,
          top: 0,
          left: 0,
        }
      ])
      .png()
      .toBuffer();

    console.log('Text overlay completed successfully');
    return result;

  } catch (error) {
    console.error('Error adding text behind object:', error);
    throw new Error('Failed to add text behind object');
  }
}

/**
 * Legacy function - adds text overlay on top (fallback)
 */
export async function addTextOverlayToImage(options: ServerTextOverlayOptions): Promise<Buffer> {
  const { imageBuffer, text, imageDimensions } = options;

  try {
    // Create SVG text overlay
    const textSVG = createTextSVG(text, imageDimensions);
    const textBuffer = Buffer.from(textSVG);

    // Composite text onto image
    const result = await sharp(imageBuffer)
      .composite([
        {
          input: textBuffer,
          top: 0,
          left: 0,
        }
      ])
      .png()
      .toBuffer();

    return result;
  } catch (error) {
    console.error('Error adding text overlay:', error);
    throw new Error('Failed to add text overlay to image');
  }
}

/**
 * Converts image to specified format with quality settings
 */
export async function convertImageFormat(
  imageBuffer: Buffer,
  format: 'png' | 'jpg',
  quality: number = 90
): Promise<Buffer> {
  try {
    const sharpInstance = sharp(imageBuffer);
    
    if (format === 'jpg') {
      return await sharpInstance
        .jpeg({ quality })
        .toBuffer();
    } else {
      return await sharpInstance
        .png({ quality })
        .toBuffer();
    }
  } catch (error) {
    console.error('Error converting image format:', error);
    throw new Error('Failed to convert image format');
  }
}

/**
 * Gets image metadata and dimensions
 */
export async function getImageMetadata(imageBuffer: Buffer): Promise<{
  width: number;
  height: number;
  format: string;
}> {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    
    if (!metadata.width || !metadata.height) {
      throw new Error('Could not determine image dimensions');
    }
    
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format || 'unknown',
    };
  } catch (error) {
    console.error('Error getting image metadata:', error);
    throw new Error('Failed to get image metadata');
  }
}
