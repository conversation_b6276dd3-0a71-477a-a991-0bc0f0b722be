// Server-side text overlay using <PERSON>

import sharp from 'sharp';
import type { TextCustomization } from '@/types/text';
import type { ImageDimensions } from '@/types/image';

interface ServerTextOverlayOptions {
  imageBuffer: Buffer;
  text: TextCustomization;
  imageDimensions: ImageDimensions;
}

/**
 * Creates an SVG text element for Sharp to composite
 */
function createTextSVG(text: TextCustomization, imageDimensions: ImageDimensions): string {
  const { content, color, fontFamily, fontSize, position } = text;
  
  // Calculate actual position in pixels
  const x = (position.x / 100) * imageDimensions.width;
  const y = (position.y / 100) * imageDimensions.height;
  
  // Create SVG with text
  const svg = `
    <svg width="${imageDimensions.width}" height="${imageDimensions.height}" xmlns="http://www.w3.org/2000/svg">
      <text
        x="${x}"
        y="${y}"
        font-family="${fontFamily}"
        font-size="${fontSize}"
        fill="${color}"
        text-anchor="middle"
        dominant-baseline="middle"
        style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));"
      >${content}</text>
    </svg>
  `;
  
  return svg;
}

/**
 * Adds text overlay to image using Sharp
 */
export async function addTextOverlayToImage(options: ServerTextOverlayOptions): Promise<Buffer> {
  const { imageBuffer, text, imageDimensions } = options;
  
  try {
    // Create SVG text overlay
    const textSVG = createTextSVG(text, imageDimensions);
    const textBuffer = Buffer.from(textSVG);
    
    // Composite text onto image
    const result = await sharp(imageBuffer)
      .composite([
        {
          input: textBuffer,
          top: 0,
          left: 0,
        }
      ])
      .png()
      .toBuffer();
    
    return result;
  } catch (error) {
    console.error('Error adding text overlay:', error);
    throw new Error('Failed to add text overlay to image');
  }
}

/**
 * Converts image to specified format with quality settings
 */
export async function convertImageFormat(
  imageBuffer: Buffer,
  format: 'png' | 'jpg',
  quality: number = 90
): Promise<Buffer> {
  try {
    const sharpInstance = sharp(imageBuffer);
    
    if (format === 'jpg') {
      return await sharpInstance
        .jpeg({ quality })
        .toBuffer();
    } else {
      return await sharpInstance
        .png({ quality })
        .toBuffer();
    }
  } catch (error) {
    console.error('Error converting image format:', error);
    throw new Error('Failed to convert image format');
  }
}

/**
 * Gets image metadata and dimensions
 */
export async function getImageMetadata(imageBuffer: Buffer): Promise<{
  width: number;
  height: number;
  format: string;
}> {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    
    if (!metadata.width || !metadata.height) {
      throw new Error('Could not determine image dimensions');
    }
    
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format || 'unknown',
    };
  } catch (error) {
    console.error('Error getting image metadata:', error);
    throw new Error('Failed to get image metadata');
  }
}
