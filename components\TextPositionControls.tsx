'use client';

import Slider from './ui/Slider';
import type { TextPosition } from '@/types/text';

interface TextPositionControlsProps {
  position: TextPosition;
  onPositionChange: (position: TextPosition) => void;
  disabled?: boolean;
}

export default function TextPositionControls({ 
  position, 
  onPositionChange, 
  disabled = false 
}: TextPositionControlsProps) {
  const handleXChange = (x: number) => {
    onPositionChange({ ...position, x });
  };

  const handleYChange = (y: number) => {
    onPositionChange({ ...position, y });
  };

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-medium text-gray-900">Text Position</h3>
      
      <div className="space-y-3">
        <Slider
          label="Horizontal Position"
          value={position.x}
          min={0}
          max={100}
          step={1}
          onChange={handleXChange}
          disabled={disabled}
        />
        
        <Slider
          label="Vertical Position"
          value={position.y}
          min={0}
          max={100}
          step={1}
          onChange={handleYChange}
          disabled={disabled}
        />
      </div>
      
      <div className="text-xs text-gray-500">
        <p>• Horizontal: 0% = Left, 100% = Right</p>
        <p>• Vertical: 0% = Top, 100% = Bottom</p>
      </div>
    </div>
  );
}
