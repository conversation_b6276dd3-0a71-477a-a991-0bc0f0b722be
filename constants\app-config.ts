// Application configuration constants

import type { AppConfig } from '@/types/global';
import type { SupportedImageFormat } from '@/types/image';
import type { TextDefaults, FontOption, ColorOption } from '@/types/text';

export const APP_CONFIG: AppConfig = {
  maxFileSize: 5 * 1024 * 1024, // 5MB in bytes
  supportedFormats: ['image/jpeg', 'image/png'],
  defaultText: 'POV',
  apiEndpoints: {
    processImage: '/api/process-image',
  },
};

export const SUPPORTED_IMAGE_FORMATS: SupportedImageFormat[] = [
  'image/jpeg',
  'image/png',
];

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const TEXT_DEFAULTS: TextDefaults = {
  content: 'POV',
  color: '#FFFFFF',
  fontFamily: 'Inter',
  fontSize: 80, // Larger text for better visibility
  position: {
    x: 30, // Position more to the left (behind object)
    y: 50, // Center vertically
  },
};

export const FONT_OPTIONS: FontOption[] = [
  { name: 'Inter', value: 'Inter', preview: 'Inter' },
  { name: 'Arial', value: 'Arial', preview: 'Arial' },
  { name: 'Helvetica', value: 'Helvetica', preview: 'Helvetica' },
  { name: 'Times New Roman', value: 'Times New Roman', preview: 'Times New Roman' },
  { name: 'Georgia', value: 'Georgia', preview: 'Georgia' },
  { name: 'Verdana', value: 'Verdana', preview: 'Verdana' },
];

export const COLOR_OPTIONS: ColorOption[] = [
  { name: 'White', value: 'white', hex: '#FFFFFF' },
  { name: 'Black', value: 'black', hex: '#000000' },
  { name: 'Red', value: 'red', hex: '#FF0000' },
  { name: 'Blue', value: 'blue', hex: '#0000FF' },
  { name: 'Green', value: 'green', hex: '#00FF00' },
  { name: 'Yellow', value: 'yellow', hex: '#FFFF00' },
  { name: 'Purple', value: 'purple', hex: '#800080' },
  { name: 'Orange', value: 'orange', hex: '#FFA500' },
];

export const FONT_SIZE_RANGE = {
  min: 12,
  max: 200,
  default: 60,
  step: 2,
};
