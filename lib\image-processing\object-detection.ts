// Object detection placeholder (will be replaced with AI implementation later)

import type { ObjectDetectionResult, ImageDimensions } from '@/types/image';

/**
 * Mock object detection function
 * This will be replaced with actual AI implementation later
 */
export async function detectObject(
  imageData: string,
  imageDimensions: ImageDimensions
): Promise<ObjectDetectionResult> {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock detection result - simulates a person in the image
  // Create more realistic human-like proportions
  const centerX = imageDimensions.width * 0.5;
  const centerY = imageDimensions.height * 0.5;
  const personWidth = imageDimensions.width * 0.25; // Person takes ~25% of width
  const personHeight = imageDimensions.height * 0.7; // Person takes ~70% of height

  const mockResult: ObjectDetectionResult = {
    detected: true,
    boundingBox: {
      x: centerX - personWidth / 2,
      y: centerY - personHeight / 2,
      width: personWidth,
      height: personHeight,
    },
    confidence: 0.92,
  };

  // Randomly simulate "no object detected" for testing
  if (Math.random() < 0.1) {
    return {
      detected: false,
    };
  }

  return mockResult;
}

/**
 * Validates detection result
 */
export function validateDetectionResult(result: ObjectDetectionResult): boolean {
  if (!result.detected) {
    return true; // Valid result even if no object detected
  }

  if (!result.boundingBox) {
    return false;
  }

  const { boundingBox } = result;
  return (
    boundingBox.x >= 0 &&
    boundingBox.y >= 0 &&
    boundingBox.width > 0 &&
    boundingBox.height > 0
  );
}

/**
 * Processes detection result for text placement
 */
export function processDetectionForTextPlacement(
  result: ObjectDetectionResult,
  imageDimensions: ImageDimensions
): ObjectDetectionResult {
  if (!result.detected || !result.boundingBox) {
    return result;
  }

  // Ensure bounding box is within image bounds
  const { boundingBox } = result;
  const clampedBoundingBox = {
    x: Math.max(0, Math.min(boundingBox.x, imageDimensions.width)),
    y: Math.max(0, Math.min(boundingBox.y, imageDimensions.height)),
    width: Math.min(boundingBox.width, imageDimensions.width - boundingBox.x),
    height: Math.min(boundingBox.height, imageDimensions.height - boundingBox.y),
  };

  return {
    ...result,
    boundingBox: clampedBoundingBox,
  };
}
