// Object detection placeholder (will be replaced with AI implementation later)

import type { ObjectDetectionResult, ImageDimensions } from '@/types/image';

/**
 * Mock object detection function
 * This will be replaced with actual AI implementation later
 */
export async function detectObject(
  imageData: string,
  imageDimensions: ImageDimensions
): Promise<ObjectDetectionResult> {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock detection result - assumes object is in center of image
  const mockResult: ObjectDetectionResult = {
    detected: true,
    boundingBox: {
      x: imageDimensions.width * 0.3,
      y: imageDimensions.height * 0.2,
      width: imageDimensions.width * 0.4,
      height: imageDimensions.height * 0.6,
    },
    confidence: 0.85,
  };

  // Randomly simulate "no object detected" for testing
  if (Math.random() < 0.1) {
    return {
      detected: false,
    };
  }

  return mockResult;
}

/**
 * Validates detection result
 */
export function validateDetectionResult(result: ObjectDetectionResult): boolean {
  if (!result.detected) {
    return true; // Valid result even if no object detected
  }

  if (!result.boundingBox) {
    return false;
  }

  const { boundingBox } = result;
  return (
    boundingBox.x >= 0 &&
    boundingBox.y >= 0 &&
    boundingBox.width > 0 &&
    boundingBox.height > 0
  );
}

/**
 * Processes detection result for text placement
 */
export function processDetectionForTextPlacement(
  result: ObjectDetectionResult,
  imageDimensions: ImageDimensions
): ObjectDetectionResult {
  if (!result.detected || !result.boundingBox) {
    return result;
  }

  // Ensure bounding box is within image bounds
  const { boundingBox } = result;
  const clampedBoundingBox = {
    x: Math.max(0, Math.min(boundingBox.x, imageDimensions.width)),
    y: Math.max(0, Math.min(boundingBox.y, imageDimensions.height)),
    width: Math.min(boundingBox.width, imageDimensions.width - boundingBox.x),
    height: Math.min(boundingBox.height, imageDimensions.height - boundingBox.y),
  };

  return {
    ...result,
    boundingBox: clampedBoundingBox,
  };
}
