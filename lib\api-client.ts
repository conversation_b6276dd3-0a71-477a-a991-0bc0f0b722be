// API client for frontend-backend communication

import { fileToBase64 } from '@/utils/helpers';
import { APP_CONFIG } from '@/constants/app-config';
import type { ImageProcessingRequest, ImageProcessingResponse } from '@/types/image';
import type { TextCustomization } from '@/types/text';

/**
 * Processes an image with text overlay
 */
export async function processImageWithText(
  imageFile: File,
  textCustomization?: Partial<TextCustomization>
): Promise<ImageProcessingResponse> {
  try {
    // Convert file to base64
    const imageData = await fileToBase64(imageFile);
    
    // Prepare request payload
    const requestData: ImageProcessingRequest = {
      imageData,
      textContent: textCustomization?.content || 'POV',
      textColor: textCustomization?.color || '#FFFFFF',
      fontFamily: textCustomization?.fontFamily || 'Inter',
      fontSize: textCustomization?.fontSize || 60,
      textPosition: textCustomization?.position || { x: 50, y: 50 },
    };

    // Make API request
    const response = await fetch(APP_CONFIG.apiEndpoints.processImage, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to process image');
    }

    const result: ImageProcessingResponse = await response.json();
    return result;

  } catch (error) {
    console.error('API request failed:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to process image',
    };
  }
}

/**
 * Downloads a processed image
 */
export function downloadImage(imageUrl: string, filename: string = 'processed-image'): void {
  try {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${filename}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Download failed:', error);
    throw new Error('Failed to download image');
  }
}
