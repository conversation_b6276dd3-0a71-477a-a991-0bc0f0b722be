// Text overlay functionality for image processing

import type { TextCustomization } from '@/types/text';
import type { ImageDimensions } from '@/types/image';

export interface TextOverlayOptions {
  text: TextCustomization;
  imageDimensions: ImageDimensions;
  canvas: HTMLCanvasElement;
}

/**
 * Renders text overlay on canvas
 */
export function renderTextOverlay(options: TextOverlayOptions): void {
  const { text, canvas } = options;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set text properties
  ctx.font = `${text.fontSize}px ${text.fontFamily}`;
  ctx.fillStyle = text.color;
  ctx.textAlign = 'left';
  ctx.textBaseline = 'top';

  // Add text shadow for better visibility
  ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
  ctx.shadowBlur = 4;
  ctx.shadowOffsetX = 2;
  ctx.shadowOffsetY = 2;

  // Render the text
  ctx.fillText(text.content, text.position.x, text.position.y);

  // Reset shadow
  ctx.shadowColor = 'transparent';
  ctx.shadowBlur = 0;
  ctx.shadowOffsetX = 0;
  ctx.shadowOffsetY = 0;
}

/**
 * Creates a canvas with image and text overlay
 */
export function createImageWithTextOverlay(
  imageElement: HTMLImageElement,
  textCustomization: TextCustomization
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }

  // Set canvas dimensions to match image
  canvas.width = imageElement.naturalWidth;
  canvas.height = imageElement.naturalHeight;

  // Draw the image
  ctx.drawImage(imageElement, 0, 0);

  // Add text overlay
  renderTextOverlay({
    text: textCustomization,
    imageDimensions: {
      width: canvas.width,
      height: canvas.height,
    },
    canvas,
  });

  return canvas;
}

/**
 * Converts canvas to blob for download
 */
export function canvasToBlob(
  canvas: HTMLCanvasElement,
  format: 'png' | 'jpg',
  quality: number = 0.9
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
    
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      mimeType,
      quality
    );
  });
}

/**
 * Downloads canvas as image file
 */
export function downloadCanvasAsImage(
  canvas: HTMLCanvasElement,
  filename: string,
  format: 'png' | 'jpg'
): void {
  const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
  const dataURL = canvas.toDataURL(mimeType, 0.9);
  
  const link = document.createElement('a');
  link.download = `${filename}.${format}`;
  link.href = dataURL;
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
