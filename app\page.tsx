'use client';

import { useImageUpload } from '@/hooks/useImageUpload';
import { useToast } from '@/hooks/useToast';
import ImageUploadForm from '@/components/ImageUploadForm';
import ToastContainer from '@/components/ui/ToastContainer';
import Button from '@/components/ui/Button';
import type { ImageFile } from '@/types/image';

export default function Home() {
  const { selectedImage, isUploading, selectImage, clearImage, setUploading } = useImageUpload();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  const handleImageSelect = (imageFile: ImageFile) => {
    selectImage(imageFile);
    showSuccess('Image uploaded', 'Your image has been uploaded successfully!');
  };

  const handleError = (error: string) => {
    showError('Upload failed', error);
  };

  const handleProcessImage = async () => {
    if (!selectedImage) return;

    setUploading(true);
    // TODO: Implement actual image processing
    setTimeout(() => {
      setUploading(false);
      showSuccess('Processing complete', 'Your image has been processed!');
    }, 2000);
  };
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Text-Behind</h1>
            </div>
            <nav className="flex space-x-8">
              <span className="text-gray-500 text-sm">Add text behind your photos, instantly</span>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Transform Your Photos with AI
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Upload an image and automatically add customizable text behind detected objects.
            Perfect for creating engaging social media content.
          </p>

          {/* Upload Area */}
          {!selectedImage ? (
            <ImageUploadForm
              onImageSelect={handleImageSelect}
              onError={handleError}
              isLoading={isUploading}
            />
          ) : (
            <div className="max-w-md mx-auto space-y-4">
              {/* Image Preview */}
              <div className="border rounded-lg overflow-hidden">
                <img
                  src={selectedImage.preview}
                  alt="Uploaded image"
                  className="w-full h-64 object-cover"
                />
              </div>

              {/* Image Info */}
              <div className="text-center text-sm text-gray-600">
                <p>{selectedImage.name}</p>
                <p>{(selectedImage.size / 1024 / 1024).toFixed(2)} MB</p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 justify-center">
                <Button
                  onClick={handleProcessImage}
                  isLoading={isUploading}
                  disabled={isUploading}
                >
                  {isUploading ? 'Processing...' : 'Add Text Behind'}
                </Button>
                <Button
                  variant="outline"
                  onClick={clearImage}
                  disabled={isUploading}
                >
                  Upload Different Image
                </Button>
              </div>
            </div>
          )}

          {/* Features */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">AI-Powered Detection</h3>
              <p className="text-gray-600">Automatically detects objects in your images for precise text placement</p>
            </div>

            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h4a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Full Customization</h3>
              <p className="text-gray-600">Customize text content, color, font, and position to match your style</p>
            </div>

            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Instant Download</h3>
              <p className="text-gray-600">Download your enhanced images in high quality PNG or JPG format</p>
            </div>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </div>
  );
}
