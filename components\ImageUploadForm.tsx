'use client';

import { useState, useRef, useCallback } from 'react';
import { validateImageFile } from '@/utils/validation';
import { createFilePreview } from '@/utils/helpers';
import Button from './ui/Button';
import Spinner from './ui/Spinner';
import type { ImageFile } from '@/types/image';

interface ImageUploadFormProps {
  onImageSelect: (imageFile: ImageFile) => void;
  onError: (error: string) => void;
  isLoading?: boolean;
}

export default function ImageUploadForm({ onImageSelect, onError, isLoading = false }: ImageUploadFormProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((file: File) => {
    const validation = validateImageFile(file);
    
    if (!validation.isValid) {
      onError(validation.error || 'Invalid file');
      return;
    }

    const preview = createFilePreview(file);
    const imageFile: ImageFile = {
      file,
      preview,
      name: file.name,
      size: file.size,
      type: file.type,
    };

    onImageSelect(imageFile);
  }, [onImageSelect, onError]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className="max-w-md mx-auto">
      <div
        className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors cursor-pointer ${
          isDragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${isLoading ? 'pointer-events-none opacity-50' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={isLoading}
        />
        
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex flex-col items-center">
              <Spinner size="lg" className="text-blue-600" />
              <p className="mt-4 text-sm text-gray-600">Processing image...</p>
            </div>
          ) : (
            <>
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 48 48">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  />
                </svg>
              </div>
              <div>
                <p className="text-sm text-gray-600">
                  <span className="font-medium text-blue-600 hover:text-blue-500">
                    Click to upload
                  </span>
                  {' '}or drag and drop
                </p>
                <p className="text-xs text-gray-500">PNG or JPG up to 5MB</p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
