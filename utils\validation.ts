// Client-side validation utilities

import { SUPPORTED_IMAGE_FORMATS, MAX_FILE_SIZE } from '@/constants/app-config';
import type { SupportedImageFormat } from '@/types/image';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validates if a file is a supported image format
 */
export function validateImageFormat(file: File): ValidationResult {
  const isSupported = SUPPORTED_IMAGE_FORMATS.includes(file.type as SupportedImageFormat);
  
  if (!isSupported) {
    return {
      isValid: false,
      error: 'Unsupported file type. Please upload a JPG or PNG image.',
    };
  }
  
  return { isValid: true };
}

/**
 * Validates if a file size is within the allowed limit
 */
export function validateFileSize(file: File): ValidationResult {
  if (file.size > MAX_FILE_SIZE) {
    const maxSizeMB = MAX_FILE_SIZE / (1024 * 1024);
    return {
      isValid: false,
      error: `File too large. Maximum size is ${maxSizeMB}MB.`,
    };
  }
  
  return { isValid: true };
}

/**
 * Validates an uploaded image file
 */
export function validateImageFile(file: File): ValidationResult {
  // Check if file exists
  if (!file) {
    return {
      isValid: false,
      error: 'Please select an image file.',
    };
  }

  // Validate format
  const formatValidation = validateImageFormat(file);
  if (!formatValidation.isValid) {
    return formatValidation;
  }

  // Validate size
  const sizeValidation = validateFileSize(file);
  if (!sizeValidation.isValid) {
    return sizeValidation;
  }

  return { isValid: true };
}

/**
 * Validates text content
 */
export function validateTextContent(content: string): ValidationResult {
  if (!content || content.trim().length === 0) {
    return {
      isValid: false,
      error: 'Text content cannot be empty.',
    };
  }

  if (content.length > 100) {
    return {
      isValid: false,
      error: 'Text content is too long. Maximum 100 characters.',
    };
  }

  return { isValid: true };
}

/**
 * Validates hex color format
 */
export function validateHexColor(color: string): ValidationResult {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  
  if (!hexRegex.test(color)) {
    return {
      isValid: false,
      error: 'Please enter a valid hex color (e.g., #FFFFFF).',
    };
  }

  return { isValid: true };
}
