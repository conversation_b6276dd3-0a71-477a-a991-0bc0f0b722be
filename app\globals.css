@import "tailwindcss";

:root {
  /* Text-Behind App Color Palette */
  --primary-accent: #4A90E2;
  --secondary-accent: #50E3C2;
  --text-primary: #333333;
  --text-secondary: #666666;
  --background-canvas: #FFFFFF;
  --background-subtle: #F8F8F8;
  --border-divider: #E0E0E0;
  --success: #4CAF50;
  --error: #FF4D4F;
  --warning: #FFA726;
  --disabled: #BBBBBB;
}

@theme inline {
  --color-primary: var(--primary-accent);
  --color-secondary: var(--secondary-accent);
  --color-background: var(--background-canvas);
  --color-foreground: var(--text-primary);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background-canvas);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-subtle);
}

::-webkit-scrollbar-thumb {
  background: var(--border-divider);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
