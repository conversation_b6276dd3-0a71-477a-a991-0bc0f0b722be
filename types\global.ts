// Global type definitions for Text-Behind app

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message: string;
}

export interface AppError {
  code: string;
  message: string;
  details?: string;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

export interface AppConfig {
  maxFileSize: number; // in bytes
  supportedFormats: string[];
  defaultText: string;
  apiEndpoints: {
    processImage: string;
  };
}

export type ComponentSize = 'sm' | 'md' | 'lg' | 'xl';
export type ComponentVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
